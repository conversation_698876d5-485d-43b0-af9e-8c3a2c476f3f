import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Sound Player App',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const SoundPlayerPage(),
    );
  }
}

class SoundPlayerPage extends StatefulWidget {
  const SoundPlayerPage({super.key});

  @override
  State<SoundPlayerPage> createState() => _SoundPlayerPageState();
}

class _SoundPlayerPageState extends State<SoundPlayerPage> {
  AudioPlayer? _currentPlayer;
  bool _isPlaying = false;

  void _playSound() async {
    try {
      // Stop any previous player
      if (_currentPlayer != null) {
        await _currentPlayer!.stop();
        await _currentPlayer!.dispose();
      }

      // Create a new player instance
      _currentPlayer = AudioPlayer();

      setState(() {
        _isPlaying = true;
      });

      // Listen for completion
      _currentPlayer!.onPlayerComplete.listen((event) {
        if (mounted) {
          setState(() {
            _isPlaying = false;
          });
        }
      });

      // Play the audio file
      await _currentPlayer!.play(AssetSource('audio/myrecord.mp3'));
    } catch (e) {
      setState(() {
        _isPlaying = false;
      });

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تشغيل الصوت: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _currentPlayer?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('اضغط على الصورة لتشغيل الصوت')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: _playSound,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _isPlaying ? Colors.green : Colors.blue,
                    width: _isPlaying ? 5 : 3,
                  ),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow:
                      _isPlaying
                          ? [
                            BoxShadow(
                              color: Colors.green.withValues(alpha: 0.3),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ]
                          : [],
                ),
                padding: const EdgeInsets.all(10),
                child: Image.asset(
                  'assets/images/speaker.png',
                  width: 200,
                  height: 200,
                ),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              _isPlaying ? 'جاري التشغيل...' : 'اضغط على الصورة لتشغيل الصوت',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
